"""
Shared configuration management for the Trend Platform
Centralizes all environment variables and settings
"""
import os
from typing import Dict, Any, List, Optional
from pydantic_settings import BaseSettings
from pydantic import field_validator
from functools import lru_cache


class Settings(BaseSettings):
    """Global application settings"""
    
    # Environment
    environment: str = "development"
    debug: bool = False
    
    # Database Configuration
    database_url: str = "postgresql://user:password@localhost:5432/trenddb"
    supabase_url: str = ""
    supabase_anon_key: str = ""
    supabase_service_role_key: str = ""
    
    # Redis Configuration
    redis_url: str = "redis://localhost:6379/0"
    celery_broker_url: str = "redis://localhost:6379/0"
    celery_result_backend: str = "redis://localhost:6379/0"
    
    # Security Configuration
    jwt_secret: str = "your-secret-key-change-in-production"
    vault_url: str = "http://localhost:8200"
    vault_token: str = ""
    encryption_key: str = "your-encryption-key-32-chars-long"
    
    # API Keys (stored in Vault in production)
    openai_api_key: str = ""
    openai_base_url: str = "https://api.openai.com/v1"
    cloudflare_api_token: str = ""
    cloudflare_zone_id: str = ""
    github_token: str = ""
    
    # External Services
    coolify_api_url: str = ""
    coolify_api_token: str = ""
    coolify_team_id: str = ""
    
    # Monitoring Configuration
    loki_url: str = "http://localhost:3100"
    prometheus_url: str = "http://localhost:9090"
    grafana_url: str = "http://localhost:3000"
    
    # Application Configuration
    app_name: str = "Trend Platform"
    app_version: str = "1.0.0"
    api_base_url: str = "http://localhost:8000"
    dashboard_url: str = "http://localhost:3000"
    
    # Content Configuration
    content_ttl_days: int = 14
    max_trends_per_scrape: int = 100
    content_generation_timeout: int = 300
    
    # Rate Limiting
    rate_limit_enabled: bool = True
    default_rate_limit: int = 100
    rate_limit_window: int = 3600
    
    # Regions and Categories
    supported_regions: List[str] = ["US", "UK", "CA", "AU", "DE", "FR", "JP"]
    supported_categories: List[str] = ["Technology", "Health", "Entertainment", "Sports", "Business"]
    
    @field_validator('database_url')
    @classmethod
    def validate_database_url(cls, v):
        if not v.startswith(('postgresql://', 'postgres://')):
            raise ValueError('Database URL must be a PostgreSQL connection string')
        return v

    @field_validator('redis_url')
    @classmethod
    def validate_redis_url(cls, v):
        if not v.startswith('redis://'):
            raise ValueError('Redis URL must be a valid Redis connection string')
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Module-specific configurations
DATABASE_CONFIG = {
    'connection': {
        'min_connections': 5,
        'max_connections': 20,
        'connection_timeout': 30,
        'command_timeout': 60,
    },
    'performance': {
        'enable_query_logging': True,
        'slow_query_threshold': 1000,  # milliseconds
        'enable_connection_pooling': True,
    }
}

SCRAPER_CONFIG = {
    'regions': ["US", "UK", "CA", "AU"],
    'categories': ["Technology", "Health", "Entertainment"],
    'proxy_rotation': {
        'enabled': True,
        'rotation_interval': 300  # 5 minutes
    },
    'rate_limiting': {
        'google_trends': {'requests_per_minute': 10},
        'trends24': {'requests_per_minute': 30}
    },
    'scoring': {
        'search_volume_weight': 0.6,
        'growth_rate_weight': 0.4,
        'minimum_score': 50
    }
}

GENERATOR_CONFIG = {
    'ai_services': {
        'text_generation': {
            'model': 'gpt-3.5-turbo',
            'max_tokens': 2000,
            'temperature': 0.7
        },
        'image_generation': {
            'model': 'dall-e-3',
            'size': '1024x1024',
            'quality': 'standard'
        }
    },
    'content': {
        'max_article_length': 2000,
        'min_article_length': 500,
        'include_code_snippets': True,
        'moderation_enabled': True
    }
}

DEPLOY_CONFIG = {
    'build': {
        'timeout': 600,
        'node_version': '18',
        'build_command': 'npm run build',
        'start_command': 'npm start'
    },
    'health_checks': {
        'enabled': True,
        'path': '/api/health',
        'timeout': 30,
        'retries': 3
    }
}

DNS_CONFIG = {
    'domains': {
        'main_domain': 'yourdomain.com',
        'trends_subdomain': 'trends.yourdomain.com',
        'pattern': '{slug}.trends.yourdomain.com'
    },
    'caching': {
        'ttl': 3600,  # 1 hour
        'worker_cache_ttl': 3600,
        'purge_on_update': True
    }
}

HOUSEKEEPING_CONFIG = {
    'content_cleanup': {
        'enabled': True,
        'default_ttl_days': 14,
        'grace_period_hours': 24,
        'batch_size': 50
    },
    'database_maintenance': {
        'enabled': True,
        'log_retention_days': 30,
        'analytics_retention_days': 90
    }
}

MONITORING_CONFIG = {
    'logging': {
        'level': 'INFO',
        'format': 'json',
        'aggregation': {
            'enabled': True,
            'buffer_size': 100,
            'flush_interval': 30
        }
    },
    'metrics': {
        'enabled': True,
        'collection_interval': 30,
        'prometheus': {
            'port': 8000,
            'path': '/metrics'
        }
    }
}

SECURITY_CONFIG = {
    'rate_limiting': {
        'enabled': True,
        'default_limits': {
            'requests': 100,
            'window': 3600  # 1 hour
        }
    },
    'input_validation': {
        'max_request_size': 10 * 1024 * 1024,  # 10MB
        'sanitization_enabled': True
    },
    'authentication': {
        'jwt_expiry': 3600,  # 1 hour
        'refresh_token_expiry': 604800,  # 7 days
    }
}

# Export commonly used settings
settings = get_settings()
