"""
AI-powered content generation system for trending topics
Generates articles, images, and code snippets using OpenAI-compatible APIs
"""
import asyncio
import aiohttp
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from shared.config import settings, GENERATOR_CONFIG
from shared.utils import AsyncHTTPClient, truncate_text
from shared.exceptions import ContentGenerationError
from monitoring.logger import get_logger
from monitoring.metrics import app_metrics
from database.models.trend_model import TrendRepository, TrendEntity
from database.models.content_model import ContentRepository, ContentCreateRequest

logger = get_logger('generator')


@dataclass
class GeneratedContent:
    """Data structure for generated content"""
    title: str
    description: str
    body: str
    meta_tags: Dict[str, str]
    hero_image_url: Optional[str] = None
    hero_image_alt: Optional[str] = None
    code_snippet: Optional[str] = None
    code_language: Optional[str] = None
    word_count: int = 0
    readability_score: Optional[float] = None
    ai_model_used: str = ""
    generation_metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.generation_metadata is None:
            self.generation_metadata = {}
        
        # Calculate word count if not provided
        if self.word_count == 0:
            self.word_count = len(self.body.split())


class AIContentGenerator:
    """AI-powered content generator using OpenAI-compatible APIs"""
    
    def __init__(self):
        self.api_key = settings.openai_api_key
        self.base_url = settings.openai_base_url
        self.text_model = GENERATOR_CONFIG['ai_services']['text_generation']['model']
        self.image_model = GENERATOR_CONFIG['ai_services']['image_generation']['model']
        self.max_tokens = GENERATOR_CONFIG['ai_services']['text_generation']['max_tokens']
        self.temperature = GENERATOR_CONFIG['ai_services']['text_generation']['temperature']
        self.logger = logger
        self.metrics = app_metrics
        
        # Content templates
        self.article_template = """
        Write a comprehensive, engaging article about "{keyword}" that is currently trending in {region}.
        
        Requirements:
        - Write 800-1500 words
        - Include an engaging introduction that explains why this topic is trending
        - Provide detailed information and context
        - Include practical examples or use cases where relevant
        - Write in a conversational, accessible tone
        - Include relevant statistics or data points if applicable
        - End with a conclusion that summarizes key points
        - Make it SEO-friendly with natural keyword usage
        
        Category: {category}
        Target audience: General readers interested in {category} trends
        
        Article:
        """
        
        self.code_snippet_template = """
        Generate a practical code example related to "{keyword}" in the {category} category.
        
        Requirements:
        - Provide working, practical code
        - Include comments explaining key parts
        - Use modern best practices
        - Make it educational and useful
        - Choose the most appropriate programming language for this topic
        
        Code example:
        """
    
    async def generate_text_content(self, prompt: str, max_tokens: int = None) -> str:
        """Generate text content using AI API"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'model': self.text_model,
                'messages': [
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'max_tokens': max_tokens or self.max_tokens,
                'temperature': self.temperature
            }
            
            async with AsyncHTTPClient(timeout=GENERATOR_CONFIG['content']['max_article_length']) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    json=payload,
                    headers=headers
                )
                
                if response['status'] != 200:
                    raise ContentGenerationError(f"AI API returned status {response['status']}")
                
                data = response['data']
                
                if 'choices' not in data or not data['choices']:
                    raise ContentGenerationError("No content generated by AI")
                
                content = data['choices'][0]['message']['content']
                
                # Record metrics
                self.metrics.record_ai_api_call('openai', self.text_model, 'text_generation')
                
                return content.strip()
                
        except Exception as e:
            self.metrics.record_ai_api_error('openai', self.text_model, 'text_generation_error')
            self.logger.error(f"Text generation failed: {str(e)}")
            raise ContentGenerationError(f"Text generation failed: {str(e)}")
    
    async def generate_image(self, prompt: str) -> Optional[str]:
        """Generate image using AI API"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'model': self.image_model,
                'prompt': prompt,
                'size': GENERATOR_CONFIG['ai_services']['image_generation']['size'],
                'quality': GENERATOR_CONFIG['ai_services']['image_generation']['quality'],
                'n': 1
            }
            
            async with AsyncHTTPClient(timeout=60) as client:
                response = await client.post(
                    f"{self.base_url}/images/generations",
                    json=payload,
                    headers=headers
                )
                
                if response['status'] != 200:
                    self.logger.warning(f"Image generation failed with status {response['status']}")
                    return None
                
                data = response['data']
                
                if 'data' not in data or not data['data']:
                    return None
                
                image_url = data['data'][0]['url']
                
                # Record metrics
                self.metrics.record_ai_api_call('openai', self.image_model, 'image_generation')
                
                return image_url
                
        except Exception as e:
            self.metrics.record_ai_api_error('openai', self.image_model, 'image_generation_error')
            self.logger.warning(f"Image generation failed: {str(e)}")
            return None
    
    def calculate_readability_score(self, text: str) -> float:
        """Calculate simple readability score (Flesch Reading Ease approximation)"""
        try:
            # Count sentences (approximate)
            sentences = len(re.findall(r'[.!?]+', text))
            if sentences == 0:
                return 0.0
            
            # Count words
            words = len(text.split())
            if words == 0:
                return 0.0
            
            # Count syllables (approximate)
            syllables = 0
            for word in text.split():
                word = word.lower().strip('.,!?;:"')
                syllable_count = len(re.findall(r'[aeiouy]+', word))
                if syllable_count == 0:
                    syllable_count = 1
                syllables += syllable_count
            
            # Flesch Reading Ease formula (simplified)
            avg_sentence_length = words / sentences
            avg_syllables_per_word = syllables / words
            
            score = 206.835 - (1.015 * avg_sentence_length) - (84.6 * avg_syllables_per_word)
            
            # Normalize to 0-100 scale
            return max(0, min(100, score))
            
        except Exception:
            return 50.0  # Default middle score
    
    def extract_code_snippet(self, content: str) -> Tuple[Optional[str], Optional[str]]:
        """Extract code snippet and language from content"""
        try:
            # Look for code blocks
            code_pattern = r'```(\w+)?\n(.*?)\n```'
            matches = re.findall(code_pattern, content, re.DOTALL)
            
            if matches:
                language, code = matches[0]
                return code.strip(), language.strip() if language else 'text'
            
            # Look for inline code
            inline_pattern = r'`([^`]+)`'
            inline_matches = re.findall(inline_pattern, content)
            
            if inline_matches and len(inline_matches[0]) > 20:  # Only longer code snippets
                return inline_matches[0], 'text'
            
            return None, None
            
        except Exception:
            return None, None
    
    async def generate_content_for_trend(self, trend: TrendEntity) -> GeneratedContent:
        """Generate complete content for a trend"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            self.logger.info(
                f"Generating content for trend: {trend.keyword}",
                trend_id=trend.id,
                keyword=trend.keyword,
                category=trend.category,
                region=trend.region
            )
            
            # Generate article content
            article_prompt = self.article_template.format(
                keyword=trend.keyword,
                region=trend.region,
                category=trend.category
            )
            
            article_content = await self.generate_text_content(article_prompt)
            
            # Extract title from content (first line or generate one)
            lines = article_content.split('\n')
            title = lines[0].strip('#').strip() if lines else f"Understanding {trend.keyword}: The Latest Trend"
            
            # Generate description
            description_prompt = f"Write a compelling 150-character meta description for an article about '{trend.keyword}' trending in {trend.region}."
            description = await self.generate_text_content(description_prompt, max_tokens=50)
            description = truncate_text(description, 160)
            
            # Generate code snippet if relevant for technology category
            code_snippet = None
            code_language = None
            if trend.category.lower() == 'technology' and GENERATOR_CONFIG['content']['include_code_snippets']:
                try:
                    code_prompt = self.code_snippet_template.format(
                        keyword=trend.keyword,
                        category=trend.category
                    )
                    code_content = await self.generate_text_content(code_prompt, max_tokens=500)
                    code_snippet, code_language = self.extract_code_snippet(code_content)
                except Exception as e:
                    self.logger.warning(f"Code generation failed: {str(e)}")
            
            # Generate hero image
            hero_image_url = None
            hero_image_alt = None
            try:
                image_prompt = f"A modern, professional illustration representing '{trend.keyword}' in the context of {trend.category}, trending topic, clean design, high quality"
                hero_image_url = await self.generate_image(image_prompt)
                if hero_image_url:
                    hero_image_alt = f"Illustration representing {trend.keyword}"
            except Exception as e:
                self.logger.warning(f"Image generation failed: {str(e)}")
            
            # Calculate metrics
            word_count = len(article_content.split())
            readability_score = self.calculate_readability_score(article_content)
            
            # Generate meta tags
            meta_tags = {
                'title': title,
                'description': description,
                'keywords': f"{trend.keyword}, {trend.category}, trending, {trend.region}",
                'og:title': title,
                'og:description': description,
                'og:type': 'article',
                'twitter:card': 'summary_large_image',
                'twitter:title': title,
                'twitter:description': description
            }
            
            if hero_image_url:
                meta_tags['og:image'] = hero_image_url
                meta_tags['twitter:image'] = hero_image_url
            
            # Create generated content object
            generated_content = GeneratedContent(
                title=title,
                description=description,
                body=article_content,
                meta_tags=meta_tags,
                hero_image_url=hero_image_url,
                hero_image_alt=hero_image_alt,
                code_snippet=code_snippet,
                code_language=code_language,
                word_count=word_count,
                readability_score=readability_score,
                ai_model_used=self.text_model,
                generation_metadata={
                    'trend_keyword': trend.keyword,
                    'trend_category': trend.category,
                    'trend_region': trend.region,
                    'generation_time': datetime.utcnow().isoformat(),
                    'prompts_used': {
                        'article': article_prompt[:200] + '...',
                        'description': description_prompt
                    }
                }
            )
            
            duration = asyncio.get_event_loop().time() - start_time
            
            # Record metrics
            self.metrics.record_content_generated(trend.category, 'openai')
            self.metrics.record_content_generation_duration('article', duration)
            
            self.logger.info(
                f"Content generation completed for trend: {trend.keyword}",
                trend_id=trend.id,
                word_count=word_count,
                readability_score=readability_score,
                duration=duration,
                has_image=hero_image_url is not None,
                has_code=code_snippet is not None
            )
            
            return generated_content
            
        except Exception as e:
            duration = asyncio.get_event_loop().time() - start_time
            self.logger.error(
                f"Content generation failed for trend: {trend.keyword} - {str(e)}",
                trend_id=trend.id,
                duration=duration,
                error=str(e)
            )
            raise ContentGenerationError(f"Content generation failed: {str(e)}")


class ContentGenerationOrchestrator:
    """Orchestrates content generation for trends"""
    
    def __init__(self):
        self.generator = AIContentGenerator()
        self.trend_repo = TrendRepository()
        self.content_repo = ContentRepository()
        self.logger = logger
        self.metrics = app_metrics
    
    async def generate_content_for_approved_trends(self) -> Dict[str, Any]:
        """Generate content for all approved trends"""
        start_time = asyncio.get_event_loop().time()
        
        results = {
            'processed': 0,
            'generated': 0,
            'errors': [],
            'trends': []
        }
        
        try:
            # Get approved trends without content
            approved_trends = await self.trend_repo.get_pending_trends(limit=10)  # Process in batches
            approved_trends = [t for t in approved_trends if t.status == 'approved']
            
            self.logger.info(f"Found {len(approved_trends)} approved trends for content generation")
            
            for trend in approved_trends:
                try:
                    results['processed'] += 1
                    
                    # Check if content already exists
                    existing_content = await self.content_repo.get_by_trend_id(trend.id)
                    if existing_content:
                        self.logger.debug(f"Content already exists for trend: {trend.keyword}")
                        continue
                    
                    # Generate content
                    generated_content = await self.generator.generate_content_for_trend(trend)
                    
                    # Save content to database
                    content_data = {
                        'trend_id': trend.id,
                        'title': generated_content.title,
                        'description': generated_content.description,
                        'body': generated_content.body,
                        'meta_tags': generated_content.meta_tags,
                        'hero_image_url': generated_content.hero_image_url,
                        'hero_image_alt': generated_content.hero_image_alt,
                        'code_snippet': generated_content.code_snippet,
                        'code_language': generated_content.code_language,
                        'word_count': generated_content.word_count,
                        'readability_score': generated_content.readability_score,
                        'ai_model_used': generated_content.ai_model_used,
                        'generation_metadata': generated_content.generation_metadata
                    }
                    
                    content_id = await self.content_repo.create(content_data)
                    
                    results['generated'] += 1
                    results['trends'].append({
                        'trend_id': trend.id,
                        'content_id': content_id,
                        'keyword': trend.keyword,
                        'word_count': generated_content.word_count
                    })
                    
                    self.logger.info(
                        f"Content generated and saved for trend: {trend.keyword}",
                        trend_id=trend.id,
                        content_id=content_id
                    )
                    
                    # Small delay between generations to avoid rate limits
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    error_msg = f"Failed to generate content for trend '{trend.keyword}': {str(e)}"
                    results['errors'].append(error_msg)
                    self.logger.error(error_msg, trend_id=trend.id)
            
            duration = asyncio.get_event_loop().time() - start_time
            
            self.logger.info(
                "Content generation batch completed",
                processed=results['processed'],
                generated=results['generated'],
                errors=len(results['errors']),
                duration=duration
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Content generation orchestration failed: {str(e)}")
            raise ContentGenerationError(f"Content generation orchestration failed: {str(e)}")


# Global orchestrator instance
content_orchestrator = ContentGenerationOrchestrator()
